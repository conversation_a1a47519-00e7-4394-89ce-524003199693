# Cloudflare Pages Configuration
name = "sayari-blog"
compatibility_date = "2024-01-01"

[build]
command = "npm run build"
publish = "dist"

# Cloudflare Speed Optimizations
[build.processing]
# Enable all Cloudflare optimizations
css = { bundle = true, minify = true }
js = { bundle = true, minify = true }
html = { minify = true }

[build.processing.images]
# Enable image optimization
format = "auto"
quality = 85
metadata = "none"

[env.production]
# Environment variables for production
# These should be set in Cloudflare Pages dashboard

[env.preview]
# Environment variables for preview deployments
